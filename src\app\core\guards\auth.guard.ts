import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, GuardResult, MaybeAsync } from '@angular/router';
import { Observable } from 'rxjs';

/**
 * AUTH GUARD - Route Protection
 * 
 * 🎯 Core Concepts Demonstrated:
 * - Route Guards (canActivate)
 * - JWT token validation
 * - Route protection strategies
 * - Redirect handling with return URLs
 * 
 * 🎤 Interview Questions This Guard Answers:
 * 
 * Q1: What are the different types of route guards in Angular?
 * A: - canActivate: Controls if a route can be activated
 *    - canActivateChild: Controls if child routes can be activated
 *    - canDeactivate: Controls if user can leave a route (unsaved changes)
 *    - canLoad: Controls if a module can be loaded (lazy loading)
 *    - resolve: Pre-fetches data before route activation
 * 
 * Q2: What does canActivate return and what do the return values mean?
 * A: Returns boolean | UrlTree | Observable<boolean | UrlTree> | Promise<boolean | UrlTree>
 *    - true: Allow navigation
 *    - false: Block navigation
 *    - UrlTree: Redirect to different route
 * 
 * Q3: How do you handle authentication in route guards?
 * A: Check authentication status, validate tokens, and redirect unauthenticated
 *    users to login with optional return URL preservation.
 * 
 * Q4: What parameters does canActivate receive?
 * A: - route: ActivatedRouteSnapshot (contains route information)
 *    - state: RouterStateSnapshot (contains router state information)
 */

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    
    // Interview Q: How do you implement authentication check?
    if (true) {
      // User is authenticated, allow access
      return true;
    }

    // Interview Q: How do you handle unauthenticated users?
    // A: Redirect to login and preserve the intended destination
    console.log('Access denied. Redirecting to login...');
    
    // Store the attempted URL for redirecting after login
    const returnUrl = state.url;
    
    // Navigate to login with return URL as query parameter
    return this.router.createUrlTree(['/login'], { 
      queryParams: { returnUrl: returnUrl }
    });
  }
}

/**
 * ADMIN GUARD - Role-based Route Protection
 * 
 * Interview Q: How do you implement role-based authentication?
 * A: Create specialized guards that check user roles/permissions
 */
@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {

  constructor(
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean | UrlTree {
    
    // First check if user is authenticated
    if (!true) {
      return this.router.createUrlTree(['/login']);
    }

    // Then check if user has admin role
    if (false) {
      return true;
    }

    // User is authenticated but not admin
    console.log('Access denied. Admin role required.');
    return this.router.createUrlTree(['/unauthorized']);
  }
}

/**
 * CAN DEACTIVATE GUARD - Prevent Navigation with Unsaved Changes
 * 
 * Interview Q: How do you prevent users from leaving a page with unsaved changes?
 * A: Use CanDeactivate guard to check component state before navigation
 */
export interface CanComponentDeactivate {
  canDeactivate: () => Observable<boolean> | Promise<boolean> | boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CanDeactivateGuard implements CanActivate {
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): MaybeAsync<GuardResult> {
    throw new Error('Method not implemented.');
  }
  
  canDeactivate(
    component: CanComponentDeactivate,
    currentRoute: ActivatedRouteSnapshot,
    currentState: RouterStateSnapshot,
    nextState?: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    // Interview Q: How does CanDeactivate work?
    // A: Calls component's canDeactivate method to check if navigation should be allowed
    return component.canDeactivate ? component.canDeactivate() : true;
  }
}

# 🚀 Angular 20-Page Mastery Project

✅ **Project Name:** `ng-mastery`
✅ **Goal:** Master modern Angular by building a **real 20-page app** that covers all key concepts you’ll need for interviews or production projects.

---

## 📌 Project Setup

```bash
ng new ng-mastery --routing --style=scss --standalone=false
cd ng-mastery
npm install @ngrx/store @ngrx/effects @angular/cdk ngx-charts @angular/material
```

---

## 🎯 Complete 20-Page Implementation Guide

Each page below includes:
- ✅ **Implementation details**
- 🎤 **Interview questions you should be able to answer**
- 💡 **Key concepts explained**
- 🔧 **Code examples**

---

## 1️⃣ Home Page

**Route:** `/`

### 🎯 Core Concepts
- Basic routing and modules
- Lazy-loaded modules
- Navigation components

### 🎤 Interview Questions & Answers

**Q: What's the difference between eager and lazy loading in Angular?**
```typescript
// Eager loading (loaded at app startup)
const routes: Routes = [
  { path: 'home', component: HomeComponent }
];

// Lazy loading (loaded on demand)
const routes: Routes = [
  {
    path: 'users',
    loadChildren: () => import('./features/users/users.module').then(m => m.UsersModule)
  }
];
```
**A:** Eager loading loads modules at application startup, while lazy loading loads modules only when the route is accessed. Lazy loading improves initial load time and reduces bundle size.

**Q: How do you implement navigation in Angular?**
```typescript
// In component
constructor(private router: Router) {}

navigateToUsers() {
  this.router.navigate(['/users']);
  // Or with parameters: this.router.navigate(['/users', userId]);
}
```

### 🔧 Implementation

```typescript
// home.component.ts
import { Component } from '@angular/core';

@Component({
  selector: 'app-home',
  template: `
    <div class="hero-section">
      <h1>Welcome to Angular Mastery</h1>
      <p>Master all 20 core Angular concepts</p>
      <nav class="quick-nav">
        <a routerLink="/login" class="nav-btn">Login Demo</a>
        <a routerLink="/users" class="nav-btn">Users API</a>
        <a routerLink="/rxjs" class="nav-btn">RxJS Playground</a>
      </nav>
    </div>
  `,
  styleUrls: ['./home.component.scss']
})
export class HomeComponent {
  // Interview Question: Why is this component class empty?
  // Answer: Not all components need logic. This is a presentation component
  // that only displays static content and navigation links.
}
```

---

## 2️⃣ Login Page

**Route:** `/login`

### 🎯 Core Concepts
- Reactive Forms (FormGroup, FormControl)
- Built-in and custom validators
- Form submission and error handling

### 🎤 Interview Questions & Answers

**Q: What's the difference between reactive and template-driven forms?**
```typescript
// Reactive Forms (Model-driven)
loginForm = this.fb.group({
  email: ['', [Validators.required, Validators.email]],
  password: ['', [Validators.required, Validators.minLength(6)]]
});

// Template-driven (in HTML)
// <input [(ngModel)]="user.email" #email="ngModel" required>
```
**A:** Reactive forms are model-driven with explicit form control in component class, offering better testability and validation. Template-driven forms are template-driven with implicit form controls using directives.

**Q: How do you create custom validators?**
```typescript
// Custom validator function
export function emailDomainValidator(domain: string): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) return null;
    const email = control.value;
    if (email.endsWith(`@${domain}`)) {
      return null; // Valid
    }
    return { emailDomain: { requiredDomain: domain, actualValue: email } };
  };
}

// Usage
email: ['', [Validators.required, emailDomainValidator('company.com')]]
```

### 🔧 Implementation

```typescript
// login.component.ts
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { Router } from '@angular/router';

// Custom validator - Interview Question: How to create custom validators?
export function noSpacesValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (control.value && control.value.indexOf(' ') >= 0) {
      return { noSpaces: { value: control.value } };
    }
    return null;
  };
}

@Component({
  selector: 'app-login',
  template: `
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <h2>Login</h2>

      <!-- Email Field -->
      <div class="form-group">
        <label>Email:</label>
        <input type="email" formControlName="email"
               [class.error]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">

        <!-- Interview Q: How to display validation errors? -->
        <div *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched" class="error-msg">
          <span *ngIf="loginForm.get('email')?.errors?.['required']">Email is required</span>
          <span *ngIf="loginForm.get('email')?.errors?.['email']">Invalid email format</span>
        </div>
      </div>

      <!-- Password Field -->
      <div class="form-group">
        <label>Password:</label>
        <input type="password" formControlName="password"
               [class.error]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">

        <div *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched" class="error-msg">
          <span *ngIf="loginForm.get('password')?.errors?.['required']">Password is required</span>
          <span *ngIf="loginForm.get('password')?.errors?.['minlength']">
            Password must be at least 6 characters
          </span>
          <span *ngIf="loginForm.get('password')?.errors?.['noSpaces']">
            Password cannot contain spaces
          </span>
        </div>
      </div>

      <button type="submit" [disabled]="loginForm.invalid" class="submit-btn">
        Login
      </button>

      <!-- Interview Q: How to show form state for debugging? -->
      <div class="debug-info" *ngIf="showDebug">
        <p>Form Valid: {{ loginForm.valid }}</p>
        <p>Form Value: {{ loginForm.value | json }}</p>
        <p>Form Errors: {{ getFormErrors() | json }}</p>
      </div>
    </form>
  `,
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  showDebug = false; // Toggle for interview demonstration

  constructor(
    private fb: FormBuilder,
    private router: Router
  ) {}

  ngOnInit() {
    // Interview Q: Why build form in ngOnInit vs constructor?
    // A: FormBuilder might not be fully initialized in constructor
    this.loginForm = this.fb.group({
      email: ['', [
        Validators.required,
        Validators.email
      ]],
      password: ['', [
        Validators.required,
        Validators.minLength(6),
        noSpacesValidator() // Custom validator
      ]]
    });
  }

  onSubmit() {
    if (this.loginForm.valid) {
      const { email, password } = this.loginForm.value;

      // Interview Q: How would you handle authentication?
      // A: Call AuthService, store JWT token, redirect on success
      console.log('Login attempt:', { email, password });

      // Simulate login success
      localStorage.setItem('token', 'fake-jwt-token');
      this.router.navigate(['/protected']);
    } else {
      // Interview Q: How to handle invalid form submission?
      // A: Mark all fields as touched to show validation errors
      this.markFormGroupTouched(this.loginForm);
    }
  }

  // Utility method for form validation
  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  // Debug helper for interviews
  getFormErrors() {
    let errors: any = {};
    Object.keys(this.loginForm.controls).forEach(key => {
      const controlErrors = this.loginForm.get(key)?.errors;
      if (controlErrors) {
        errors[key] = controlErrors;
      }
    });
    return errors;
  }
}
```

---

## 3️⃣ Register Page

**Route:** `/register`

### 🎯 Core Concepts
- Template-driven forms
- Two-way data binding with ngModel
- Comparison with reactive forms

### 🎤 Interview Questions & Answers

**Q: When would you use template-driven forms vs reactive forms?**
**A:**
- **Template-driven:** Simple forms, rapid prototyping, less complex validation
- **Reactive:** Complex forms, dynamic forms, extensive validation, better testing

**Q: How does two-way data binding work in Angular?**
```typescript
// Two-way binding syntax
<input [(ngModel)]="user.name">

// Equivalent to:
<input [ngModel]="user.name" (ngModelChange)="user.name = $event">
```

### 🔧 Implementation

```typescript
// register.component.ts
import { Component } from '@angular/core';

interface User {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  agreeToTerms: boolean;
}

@Component({
  selector: 'app-register',
  template: `
    <form #registerForm="ngForm" (ngSubmit)="onSubmit(registerForm)" class="register-form">
      <h2>Register</h2>

      <!-- Interview Q: How does template reference variable work? -->
      <!-- A: #registerForm creates a reference to the NgForm directive -->

      <div class="form-group">
        <label>Full Name:</label>
        <input type="text"
               name="name"
               [(ngModel)]="user.name"
               #name="ngModel"
               required
               minlength="2"
               [class.error]="name.invalid && name.touched">

        <!-- Interview Q: How to access form control in template-driven forms? -->
        <!-- A: Use template reference variable with ngModel -->
        <div *ngIf="name.invalid && name.touched" class="error-msg">
          <span *ngIf="name.errors?.['required']">Name is required</span>
          <span *ngIf="name.errors?.['minlength']">Name must be at least 2 characters</span>
        </div>
      </div>

      <div class="form-group">
        <label>Email:</label>
        <input type="email"
               name="email"
               [(ngModel)]="user.email"
               #email="ngModel"
               required
               email
               [class.error]="email.invalid && email.touched">

        <div *ngIf="email.invalid && email.touched" class="error-msg">
          <span *ngIf="email.errors?.['required']">Email is required</span>
          <span *ngIf="email.errors?.['email']">Invalid email format</span>
        </div>
      </div>

      <div class="form-group">
        <label>Password:</label>
        <input type="password"
               name="password"
               [(ngModel)]="user.password"
               #password="ngModel"
               required
               minlength="6"
               [class.error]="password.invalid && password.touched">

        <div *ngIf="password.invalid && password.touched" class="error-msg">
          <span *ngIf="password.errors?.['required']">Password is required</span>
          <span *ngIf="password.errors?.['minlength']">Password must be at least 6 characters</span>
        </div>
      </div>

      <div class="form-group">
        <label>Confirm Password:</label>
        <input type="password"
               name="confirmPassword"
               [(ngModel)]="user.confirmPassword"
               #confirmPassword="ngModel"
               required
               [class.error]="confirmPassword.invalid && confirmPassword.touched || !passwordsMatch()">

        <!-- Interview Q: How to implement cross-field validation in template-driven forms? -->
        <div *ngIf="(confirmPassword.touched && !passwordsMatch()) || (confirmPassword.invalid && confirmPassword.touched)" class="error-msg">
          <span *ngIf="confirmPassword.errors?.['required']">Please confirm your password</span>
          <span *ngIf="confirmPassword.valid && !passwordsMatch()">Passwords do not match</span>
        </div>
      </div>

      <div class="form-group">
        <label class="checkbox-label">
          <input type="checkbox"
                 name="agreeToTerms"
                 [(ngModel)]="user.agreeToTerms"
                 #terms="ngModel"
                 required>
          I agree to the terms and conditions
        </label>

        <div *ngIf="terms.invalid && terms.touched" class="error-msg">
          <span *ngIf="terms.errors?.['required']">You must agree to the terms</span>
        </div>
      </div>

      <button type="submit"
              [disabled]="registerForm.invalid || !passwordsMatch()"
              class="submit-btn">
        Register
      </button>

      <!-- Debug info for interviews -->
      <div class="debug-info" *ngIf="showDebug">
        <p>Form Valid: {{ registerForm.valid }}</p>
        <p>Passwords Match: {{ passwordsMatch() }}</p>
        <p>User Data: {{ user | json }}</p>
      </div>
    </form>
  `,
  styleUrls: ['./register.component.scss']
})
export class RegisterComponent {
  showDebug = false;

  user: User = {
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false
  };

  // Interview Q: How to implement custom validation logic?
  passwordsMatch(): boolean {
    return this.user.password === this.user.confirmPassword;
  }

  onSubmit(form: any) {
    if (form.valid && this.passwordsMatch()) {
      console.log('Registration data:', this.user);
      // Interview Q: What would you do next?
      // A: Call registration API, handle success/error, redirect to login
    } else {
      console.log('Form is invalid');
      // Interview Q: How to show validation errors on submit?
      // A: Mark all fields as touched or use form.submitted
    }
  }
}
```

---

## 4️⃣ Protected Page

**Route:** `/protected`

### 🎯 Core Concepts
- Route Guards (canActivate)
- JWT token authentication
- Route protection strategies

### 🎤 Interview Questions & Answers

**Q: What are the different types of route guards in Angular?**
**A:**
- **canActivate:** Controls if a route can be activated
- **canActivateChild:** Controls if child routes can be activated
- **canDeactivate:** Controls if user can leave a route
- **canLoad:** Controls if a module can be loaded (for lazy loading)
- **resolve:** Pre-fetches data before route activation

**Q: How do you implement JWT authentication in Angular?**
```typescript
// AuthService
isAuthenticated(): boolean {
  const token = localStorage.getItem('token');
  if (!token) return false;

  // Check if token is expired
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp > Date.now() / 1000;
  } catch {
    return false;
  }
}
```

### 🔧 Implementation

```typescript
// auth.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    // Interview Q: What parameters does canActivate receive?
    // A: ActivatedRouteSnapshot (route info) and RouterStateSnapshot (router state)

    if (this.authService.isAuthenticated()) {
      return true;
    }

    // Interview Q: How to redirect unauthenticated users?
    // A: Use router.navigate() and return false
    this.router.navigate(['/login'], {
      queryParams: { returnUrl: state.url } // Save intended destination
    });
    return false;
  }
}

// auth.service.ts
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  isAuthenticated(): boolean {
    const token = localStorage.getItem('token');
    return !!token; // Simplified for demo
  }

  logout(): void {
    localStorage.removeItem('token');
  }

  // Interview Q: How to decode JWT token?
  getTokenPayload(): any {
    const token = localStorage.getItem('token');
    if (!token) return null;

    try {
      return JSON.parse(atob(token.split('.')[1]));
    } catch {
      return null;
    }
  }
}

// protected.component.ts
import { Component, OnInit } from '@angular/core';
import { AuthService } from '../core/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-protected',
  template: `
    <div class="protected-content">
      <h2>🔒 Protected Area</h2>
      <p>You can only see this if you're authenticated!</p>

      <div class="user-info" *ngIf="userInfo">
        <h3>Token Information:</h3>
        <pre>{{ userInfo | json }}</pre>
      </div>

      <div class="actions">
        <button (click)="logout()" class="logout-btn">Logout</button>
        <button (click)="refreshData()" class="refresh-btn">Refresh Data</button>
      </div>

      <!-- Interview Q: How to show loading states? -->
      <div *ngIf="loading" class="loading">Loading protected data...</div>

      <div *ngIf="protectedData" class="data-section">
        <h3>Protected Data:</h3>
        <ul>
          <li *ngFor="let item of protectedData">{{ item }}</li>
        </ul>
      </div>
    </div>
  `,
  styleUrls: ['./protected.component.scss']
})
export class ProtectedComponent implements OnInit {
  userInfo: any;
  protectedData: string[] = [];
  loading = false;

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit() {
    // Interview Q: What should you do in ngOnInit for protected routes?
    // A: Verify authentication, load user-specific data, set up subscriptions
    this.userInfo = this.authService.getTokenPayload();
    this.loadProtectedData();
  }

  loadProtectedData() {
    this.loading = true;

    // Simulate API call
    setTimeout(() => {
      this.protectedData = [
        'Secret data item 1',
        'Confidential information 2',
        'Protected resource 3'
      ];
      this.loading = false;
    }, 1000);
  }

  refreshData() {
    this.loadProtectedData();
  }

  logout() {
    // Interview Q: What should happen during logout?
    // A: Clear tokens, clear user data, redirect to login
    this.authService.logout();
    this.router.navigate(['/login']);
  }
}
```

---

## 5️⃣ Users List Page

**Route:** `/users`

### 🎯 Core Concepts
- HttpClient for API calls
- Async pipe for observables
- Error handling in HTTP requests

### 🎤 Interview Questions & Answers

**Q: What's the difference between subscribe() and async pipe?**
```typescript
// Using subscribe (manual subscription management)
ngOnInit() {
  this.userService.getUsers().subscribe(users => {
    this.users = users;
  });
}
ngOnDestroy() {
  // Must unsubscribe manually
}

// Using async pipe (automatic subscription management)
users$ = this.userService.getUsers(); // In template: users$ | async
```
**A:** Async pipe automatically subscribes/unsubscribes, preventing memory leaks. Manual subscribe requires cleanup in ngOnDestroy.

**Q: How do you handle HTTP errors in Angular?**
```typescript
getUsers(): Observable<User[]> {
  return this.http.get<User[]>('/api/users').pipe(
    catchError(error => {
      console.error('Error fetching users:', error);
      return throwError(() => error);
    })
  );
}
```

### 🔧 Implementation

```typescript
// user.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { catchError, delay, map } from 'rxjs/operators';

export interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
  website: string;
  company: {
    name: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private apiUrl = 'https://jsonplaceholder.typicode.com/users';

  constructor(private http: HttpClient) {}

  // Interview Q: How to type HTTP responses?
  getUsers(): Observable<User[]> {
    return this.http.get<User[]>(this.apiUrl).pipe(
      delay(500), // Simulate network delay
      catchError(this.handleError)
    );
  }

  getUserById(id: number): Observable<User> {
    return this.http.get<User>(`${this.apiUrl}/${id}`).pipe(
      catchError(this.handleError)
    );
  }

  // Interview Q: How to implement proper error handling?
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Server Error Code: ${error.status}\nMessage: ${error.message}`;
    }

    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }

  // Interview Q: How to implement search functionality?
  searchUsers(query: string): Observable<User[]> {
    if (!query.trim()) {
      return this.getUsers();
    }

    return this.getUsers().pipe(
      map(users => users.filter(user =>
        user.name.toLowerCase().includes(query.toLowerCase()) ||
        user.email.toLowerCase().includes(query.toLowerCase())
      ))
    );
  }
}

// users-list.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Observable, Subject, BehaviorSubject, combineLatest } from 'rxjs';
import { map, startWith, debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';
import { UserService, User } from '../core/user.service';

@Component({
  selector: 'app-users-list',
  template: `
    <div class="users-container">
      <h2>Users List</h2>

      <!-- Search functionality -->
      <div class="search-section">
        <input type="text"
               placeholder="Search users..."
               (input)="onSearchChange($event)"
               class="search-input">
      </div>

      <!-- Loading state -->
      <div *ngIf="loading$ | async" class="loading">
        Loading users...
      </div>

      <!-- Error state -->
      <div *ngIf="error$ | async as error" class="error">
        {{ error }}
        <button (click)="retry()" class="retry-btn">Retry</button>
      </div>

      <!-- Users list -->
      <!-- Interview Q: Why use async pipe instead of subscribe? -->
      <!-- A: Automatic subscription management, prevents memory leaks -->
      <div class="users-grid" *ngIf="filteredUsers$ | async as users">
        <div class="user-card" *ngFor="let user of users; trackBy: trackByUserId">
          <h3>{{ user.name }}</h3>
          <p>📧 {{ user.email }}</p>
          <p>📞 {{ user.phone }}</p>
          <p>🌐 {{ user.website }}</p>
          <p>🏢 {{ user.company.name }}</p>

          <div class="card-actions">
            <button (click)="viewUser(user.id)" class="view-btn">View Details</button>
          </div>
        </div>
      </div>

      <!-- Empty state -->
      <div *ngIf="(filteredUsers$ | async)?.length === 0" class="empty-state">
        No users found matching your search.
      </div>
    </div>
  `,
  styleUrls: ['./users-list.component.scss']
})
export class UsersListComponent implements OnInit, OnDestroy {
  // Interview Q: What's the difference between Subject and BehaviorSubject?
  // A: BehaviorSubject has initial value and emits current value to new subscribers
  private searchSubject = new Subject<string>();
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private errorSubject = new BehaviorSubject<string | null>(null);

  users$!: Observable<User[]>;
  filteredUsers$!: Observable<User[]>;
  loading$ = this.loadingSubject.asObservable();
  error$ = this.errorSubject.asObservable();

  constructor(
    private userService: UserService,
    private router: Router
  ) {}

  ngOnInit() {
    this.setupDataStreams();
  }

  ngOnDestroy() {
    // Interview Q: Why complete subjects in ngOnDestroy?
    // A: Prevents memory leaks and ensures proper cleanup
    this.searchSubject.complete();
    this.loadingSubject.complete();
    this.errorSubject.complete();
  }

  private setupDataStreams() {
    // Interview Q: How to combine multiple observables?
    // A: Use combineLatest, merge, or other RxJS operators

    const search$ = this.searchSubject.pipe(
      startWith(''),
      debounceTime(300),
      distinctUntilChanged()
    );

    this.filteredUsers$ = search$.pipe(
      switchMap(query => {
        this.loadingSubject.next(true);
        this.errorSubject.next(null);

        return this.userService.searchUsers(query).pipe(
          map(users => {
            this.loadingSubject.next(false);
            return users;
          }),
          catchError(error => {
            this.loadingSubject.next(false);
            this.errorSubject.next(error.message);
            return of([]); // Return empty array on error
          })
        );
      })
    );
  }

  onSearchChange(event: any) {
    this.searchSubject.next(event.target.value);
  }

  // Interview Q: What is trackBy and why use it?
  // A: Improves performance by helping Angular identify which items changed
  trackByUserId(index: number, user: User): number {
    return user.id;
  }

  viewUser(userId: number) {
    this.router.navigate(['/users', userId]);
  }

  retry() {
    this.searchSubject.next(''); // Trigger reload
  }
}
```

---

## 6️⃣ User Details Page

**Route:** `/users/:id`

### 🎯 Core Concepts
- Route parameters with ActivatedRoute
- Route Resolvers for data pre-fetching
- Navigation between routes

### 🎤 Interview Questions & Answers

**Q: What's the difference between route params, query params, and fragments?**
```typescript
// Route: /users/123?tab=profile#section1
// Params: { id: '123' }
// Query Params: { tab: 'profile' }
// Fragment: 'section1'

this.route.params.subscribe(params => console.log(params.id));
this.route.queryParams.subscribe(query => console.log(query.tab));
this.route.fragment.subscribe(fragment => console.log(fragment));
```

**Q: What are Route Resolvers and when would you use them?**
**A:** Resolvers pre-fetch data before route activation, ensuring data is available when component initializes. Use for critical data that component needs immediately.

### 🔧 Implementation

```typescript
// user-resolver.service.ts
import { Injectable } from '@angular/core';
import { Resolve, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { UserService, User } from '../core/user.service';

@Injectable({
  providedIn: 'root'
})
export class UserResolver implements Resolve<User | null> {

  constructor(private userService: UserService) {}

  // Interview Q: What does a resolver return?
  // A: Observable, Promise, or direct value. Null/undefined for error cases.
  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<User | null> {
    const userId = Number(route.paramMap.get('id'));

    if (!userId) {
      return of(null);
    }

    return this.userService.getUserById(userId).pipe(
      catchError(error => {
        console.error('Error resolving user:', error);
        return of(null); // Return null on error
      })
    );
  }
}

// user-detail.component.ts
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { User } from '../core/user.service';

@Component({
  selector: 'app-user-detail',
  template: `
    <div class="user-detail-container">
      <!-- Navigation -->
      <div class="nav-section">
        <button (click)="goBack()" class="back-btn">← Back to Users</button>
        <button (click)="goToNext()" [disabled]="!hasNext()" class="next-btn">Next User →</button>
      </div>

      <!-- User not found -->
      <div *ngIf="!user" class="error-state">
        <h2>User Not Found</h2>
        <p>The requested user could not be found.</p>
        <button (click)="goBack()" class="back-btn">Go Back</button>
      </div>

      <!-- User details -->
      <div *ngIf="user" class="user-details">
        <div class="user-header">
          <h1>{{ user.name }}</h1>
          <span class="user-id">ID: {{ user.id }}</span>
        </div>

        <div class="user-info-grid">
          <div class="info-card">
            <h3>Contact Information</h3>
            <p><strong>Email:</strong> {{ user.email }}</p>
            <p><strong>Phone:</strong> {{ user.phone }}</p>
            <p><strong>Website:</strong>
              <a [href]="'http://' + user.website" target="_blank">{{ user.website }}</a>
            </p>
          </div>

          <div class="info-card">
            <h3>Address</h3>
            <p>{{ user.address?.street }}, {{ user.address?.suite }}</p>
            <p>{{ user.address?.city }}, {{ user.address?.zipcode }}</p>
            <p><strong>Coordinates:</strong> {{ user.address?.geo?.lat }}, {{ user.address?.geo?.lng }}</p>
          </div>

          <div class="info-card">
            <h3>Company</h3>
            <p><strong>Name:</strong> {{ user.company?.name }}</p>
            <p><strong>Catchphrase:</strong> {{ user.company?.catchPhrase }}</p>
            <p><strong>Business:</strong> {{ user.company?.bs }}</p>
          </div>
        </div>

        <!-- Actions -->
        <div class="actions-section">
          <button (click)="editUser()" class="edit-btn">Edit User</button>
          <button (click)="deleteUser()" class="delete-btn">Delete User</button>
          <button (click)="viewPosts()" class="posts-btn">View Posts</button>
        </div>

        <!-- Debug info for interviews -->
        <div class="debug-section" *ngIf="showDebug">
          <h3>Route Information:</h3>
          <p><strong>Current ID:</strong> {{ currentUserId }}</p>
          <p><strong>Query Params:</strong> {{ queryParams | json }}</p>
          <p><strong>Fragment:</strong> {{ fragment }}</p>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./user-detail.component.scss']
})
export class UserDetailComponent implements OnInit {
  user: User | null = null;
  currentUserId!: number;
  queryParams: any = {};
  fragment: string | null = null;
  showDebug = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private location: Location
  ) {}

  ngOnInit() {
    // Interview Q: How to get data from resolver?
    // A: Access route.data or route.snapshot.data
    this.user = this.route.snapshot.data['user'];

    // Interview Q: How to get route parameters?
    this.currentUserId = Number(this.route.snapshot.paramMap.get('id'));

    // Interview Q: How to subscribe to route changes?
    // A: Subscribe to route.params, route.queryParams, etc.
    this.route.params.subscribe(params => {
      this.currentUserId = Number(params['id']);
    });

    this.route.queryParams.subscribe(params => {
      this.queryParams = params;
    });

    this.route.fragment.subscribe(fragment => {
      this.fragment = fragment;
    });
  }

  goBack() {
    // Interview Q: What's the difference between router.navigate and location.back?
    // A: location.back() goes to previous page in browser history
    // router.navigate() goes to specific route
    this.location.back();
  }

  goToNext() {
    const nextId = this.currentUserId + 1;
    this.router.navigate(['/users', nextId]);
  }

  hasNext(): boolean {
    return this.currentUserId < 10; // Assuming 10 users max
  }

  editUser() {
    // Interview Q: How to pass data between routes?
    // A: Query params, route state, or shared service
    this.router.navigate(['/users', this.currentUserId, 'edit'], {
      queryParams: { mode: 'edit' }
    });
  }

  deleteUser() {
    if (confirm('Are you sure you want to delete this user?')) {
      // Simulate delete operation
      console.log('Deleting user:', this.currentUserId);
      this.router.navigate(['/users']);
    }
  }

  viewPosts() {
    this.router.navigate(['/users', this.currentUserId, 'posts']);
  }
}
```

---

## 7️⃣ Filter Box Page

**Route:** `/filter`

### 🎯 Core Concepts
- RxJS operators (debounceTime, distinctUntilChanged)
- Real-time filtering
- Performance optimization

### 🎤 Interview Questions & Answers

**Q: Why use debounceTime and distinctUntilChanged for search?**
```typescript
searchControl.valueChanges.pipe(
  debounceTime(300),        // Wait 300ms after user stops typing
  distinctUntilChanged(),   // Only emit if value actually changed
  switchMap(query => this.searchService.search(query))
)
```
**A:** debounceTime prevents excessive API calls while user is typing. distinctUntilChanged prevents duplicate requests for same search term.

**Q: What's the difference between switchMap, mergeMap, and concatMap?**
**A:**
- **switchMap:** Cancels previous inner observable (good for search)
- **mergeMap:** Runs inner observables concurrently (good for independent requests)
- **concatMap:** Runs inner observables sequentially (good for ordered operations)

### 🔧 Implementation

```typescript
// filter.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Observable, Subject, BehaviorSubject, combineLatest } from 'rxjs';
import { debounceTime, distinctUntilChanged, startWith, map, takeUntil } from 'rxjs/operators';

interface Product {
  id: number;
  name: string;
  category: string;
  price: number;
  description: string;
  inStock: boolean;
}

@Component({
  selector: 'app-filter',
  template: `
    <div class="filter-container">
      <h2>Real-time Filter Demo</h2>

      <div class="filter-controls">
        <!-- Search input -->
        <div class="control-group">
          <label>Search Products:</label>
          <input [formControl]="searchControl"
                 placeholder="Type to search..."
                 class="search-input">
          <small>Uses debounceTime(300) + distinctUntilChanged()</small>
        </div>

        <!-- Category filter -->
        <div class="control-group">
          <label>Category:</label>
          <select [formControl]="categoryControl" class="category-select">
            <option value="">All Categories</option>
            <option value="electronics">Electronics</option>
            <option value="clothing">Clothing</option>
            <option value="books">Books</option>
            <option value="home">Home & Garden</option>
          </select>
        </div>

        <!-- Price range -->
        <div class="control-group">
          <label>Max Price: ${{ maxPriceControl.value }}</label>
          <input type="range"
                 [formControl]="maxPriceControl"
                 min="0"
                 max="1000"
                 step="10"
                 class="price-slider">
        </div>

        <!-- In stock filter -->
        <div class="control-group">
          <label class="checkbox-label">
            <input type="checkbox" [formControl]="inStockControl">
            In Stock Only
          </label>
        </div>
      </div>

      <!-- Filter results info -->
      <div class="results-info">
        <p>Showing {{ (filteredProducts$ | async)?.length }} of {{ allProducts.length }} products</p>
        <p *ngIf="searchControl.value">Search: "{{ searchControl.value }}"</p>

        <!-- Debug info for interviews -->
        <div class="debug-info" *ngIf="showDebug">
          <h4>Filter Values:</h4>
          <pre>{{ getFilterValues() | json }}</pre>
        </div>
      </div>

      <!-- Products grid -->
      <div class="products-grid">
        <div class="product-card"
             *ngFor="let product of filteredProducts$ | async; trackBy: trackByProductId">
          <h3>{{ product.name }}</h3>
          <p class="category">{{ product.category | titlecase }}</p>
          <p class="price">${{ product.price }}</p>
          <p class="description">{{ product.description }}</p>
          <span class="stock-status"
                [class.in-stock]="product.inStock"
                [class.out-of-stock]="!product.inStock">
            {{ product.inStock ? 'In Stock' : 'Out of Stock' }}
          </span>
        </div>
      </div>

      <!-- No results -->
      <div *ngIf="(filteredProducts$ | async)?.length === 0" class="no-results">
        <h3>No products found</h3>
        <p>Try adjusting your filters</p>
        <button (click)="clearFilters()" class="clear-btn">Clear All Filters</button>
      </div>
    </div>
  `,
  styleUrls: ['./filter.component.scss']
})
export class FilterComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  showDebug = false;

  // Form controls for each filter
  searchControl = new FormControl('');
  categoryControl = new FormControl('');
  maxPriceControl = new FormControl(1000);
  inStockControl = new FormControl(false);

  // Sample data
  allProducts: Product[] = [
    { id: 1, name: 'iPhone 13', category: 'electronics', price: 699, description: 'Latest smartphone', inStock: true },
    { id: 2, name: 'MacBook Pro', category: 'electronics', price: 1299, description: 'Powerful laptop', inStock: false },
    { id: 3, name: 'Cotton T-Shirt', category: 'clothing', price: 25, description: 'Comfortable cotton tee', inStock: true },
    { id: 4, name: 'JavaScript Guide', category: 'books', price: 45, description: 'Learn JavaScript', inStock: true },
    { id: 5, name: 'Garden Hose', category: 'home', price: 35, description: '50ft garden hose', inStock: false },
    { id: 6, name: 'Wireless Headphones', category: 'electronics', price: 199, description: 'Noise cancelling', inStock: true },
    { id: 7, name: 'Jeans', category: 'clothing', price: 89, description: 'Classic blue jeans', inStock: true },
    { id: 8, name: 'Angular Cookbook', category: 'books', price: 55, description: 'Angular recipes', inStock: false }
  ];

  filteredProducts$!: Observable<Product[]>;

  ngOnInit() {
    this.setupFiltering();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupFiltering() {
    // Interview Q: How to combine multiple form controls?
    // A: Use combineLatest to react to changes in any control

    const search$ = this.searchControl.valueChanges.pipe(
      startWith(''),
      debounceTime(300),
      distinctUntilChanged()
    );

    const category$ = this.categoryControl.valueChanges.pipe(
      startWith('')
    );

    const maxPrice$ = this.maxPriceControl.valueChanges.pipe(
      startWith(1000)
    );

    const inStock$ = this.inStockControl.valueChanges.pipe(
      startWith(false)
    );

    // Interview Q: How to combine multiple filter criteria?
    this.filteredProducts$ = combineLatest([
      search$,
      category$,
      maxPrice$,
      inStock$
    ]).pipe(
      map(([searchTerm, category, maxPrice, inStockOnly]) => {
        return this.allProducts.filter(product => {
          // Search filter
          const matchesSearch = !searchTerm ||
            product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            product.description.toLowerCase().includes(searchTerm.toLowerCase());

          // Category filter
          const matchesCategory = !category || product.category === category;

          // Price filter
          const matchesPrice = product.price <= maxPrice;

          // Stock filter
          const matchesStock = !inStockOnly || product.inStock;

          return matchesSearch && matchesCategory && matchesPrice && matchesStock;
        });
      }),
      takeUntil(this.destroy$)
    );
  }

  trackByProductId(index: number, product: Product): number {
    return product.id;
  }

  clearFilters() {
    this.searchControl.setValue('');
    this.categoryControl.setValue('');
    this.maxPriceControl.setValue(1000);
    this.inStockControl.setValue(false);
  }

  getFilterValues() {
    return {
      search: this.searchControl.value,
      category: this.categoryControl.value,
      maxPrice: this.maxPriceControl.value,
      inStockOnly: this.inStockControl.value
    };
  }
}
```
```

# 🚀 Angular 20-Page Mastery Project

✅ **Project Name:** `ng-mastery`  
✅ **Goal:** Master modern Angular by building a **real 20-page app** that covers all key concepts you’ll need for interviews or production projects.

---

## 📌 Project Setup

ng new ng-mastery --routing --style=scss --standalone=false

✅ Pages & Core Topics

Below is the page list, the concept it covers, and key ideas you can explain in interviews.
1️⃣ Home Page

Route: /

    Basic routing, modules.

    Lazy-loaded module.

    Simple NavbarComponent with router links.

2️⃣ Login

Route: /login

    Reactive Form with FormGroup & FormControl.

    Built-in & custom validators.

    Submit & handle errors.

3️⃣ Register

Route: /register

    Template-driven form.

    Shows two-way binding with ngModel.

    Compare with reactive forms.

4️⃣ Protected Page

Route: /protected

    AuthGuard with canActivate.

    Checks JWT token in localStorage.

5️⃣ Users List

Route: /users

    Uses HttpClient to fetch fake API.

    Display list with *ngFor.

    Uses async pipe.

6️⃣ User Details

Route: /users/:id

    Uses Route Resolver to pre-fetch data.

    Uses ActivatedRoute to get params.

7️⃣ Filter Box

Route: /filter

    Custom FilterComponent.

    Uses debounceTime + distinctUntilChanged with RxJS.

    Filters a list in real time.

8️⃣ Chart Page

Route: /chart

    Integrate ngx-charts or highcharts-angular.

    Uses RxJS interval() to push live data.

9️⃣ File Upload

Route: /upload

    Upload CSV file.

    Preview rows in a table.

    Parses file with FileReader.

🔟 RxJS Playground

Route: /rxjs

    forkJoin → Multiple API calls in parallel.

    combineLatest → Combine user input + config.

    switchMap → Search API with cancel.

    concat, zip, race → Show differences.

1️⃣1️⃣ BehaviorSubject Demo

Route: /state

    Shared service with BehaviorSubject.

    2 sibling components read/write same state.

1️⃣2️⃣ NgRx Store Example

Route: /ngrx

    Simple posts slice.

    Actions, Reducers, Selectors.

    Dispatch → State → DevTools.

1️⃣3️⃣ Pipe Demo

Route: /pipe

    Custom CapitalizePipe.

    Pure vs impure pipe.

    Shows performance impact.

1️⃣4️⃣ Directive Demo

Route: /directive

    Custom HighlightDirective (change bg on hover).

    Structural directive: *appIfAdmin.

1️⃣5️⃣ CSS Encapsulation

Route: /styles

    ViewEncapsulation.None vs Emulated vs ShadowDom.

    Shows style leak vs isolation.

1️⃣6️⃣ Dynamic Form

Route: /dynamic-form

    Build form from JSON schema.

    Add/remove fields at runtime.

1️⃣7️⃣ Parent-Child API

Route: /parent-child

    Child component calls API.

    Emits data up to parent with @Output.

1️⃣8️⃣ Infinite Scroll

Route: /scroll

    Virtual scroll with CDK.

    Load more data on scroll.

    Uses RxJS concat.

1️⃣9️⃣ Auth Interceptor

Route: /auth-interceptor

    Adds JWT Authorization header globally.

    Handles 401 errors.

2️⃣0️⃣ Web Worker

Route: /worker

    Parse huge JSON file with a Web Worker.

    Keeps UI smooth.

    Communicate with postMessage.

✅ Folder Structure

/src/app/
  core/               # services, guards, interceptors
  shared/             # pipes, directives, reusable components
  features/           # each page in a feature module
  assets/             # sample JSON, CSV

✅ Suggested Libraries

    ngx-charts or highcharts-angular

    @ngrx/store

    @angular/cdk for virtual scroll

✅ How to Scaffold

ng generate module features/users --route users --module app
ng generate component features/users/list
ng generate guard core/auth
ng generate service core/auth
ng generate pipe shared/capitalize
ng generate directive shared/highlight

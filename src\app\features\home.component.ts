import { Component } from '@angular/core';
import { Router } from '@angular/router';

/**
 * HOME COMPONENT - Page 1 of 20
 * 
 * 🎯 Core Concepts Demonstrated:
 * - Basic component structure
 * - Router navigation
 * - Template interpolation
 * - Event binding
 * 
 * 🎤 Interview Questions This Component Answers:
 * 
 * Q1: What is a component in Angular?
 * A: A component is a TypeScript class decorated with @Component that controls
 *    a patch of screen called a view. It consists of:
 *    - Component class (logic)
 *    - HTML template (view)
 *    - CSS styles (styling)
 * 
 * Q2: How do you navigate programmatically in Angular?
 * A: Use Router.navigate() method:
 *    - this.router.navigate(['/path'])
 *    - this.router.navigate(['/path', param])
 *    - this.router.navigate(['/path'], { queryParams: { key: 'value' } })
 * 
 * Q3: What's the difference between constructor and ngOnInit?
 * A: Constructor is for dependency injection and basic initialization.
 *    ngOnInit is for component initialization logic after Angular sets up
 *    data-bound properties.
 */

@Component({
  selector: 'app-home',
  template: `
    <div class="home-container">
      <!-- Hero Section -->
      <section class="hero">
        <h1 class="hero-title">🚀 Angular 20-Page Mastery</h1>
        <p class="hero-subtitle">
          Master all core Angular concepts through 20 practical components
        </p>
        <div class="hero-stats">
          <div class="stat">
            <span class="stat-number">20</span>
            <span class="stat-label">Components</span>
          </div>
          <div class="stat">
            <span class="stat-number">50+</span>
            <span class="stat-label">Interview Questions</span>
          </div>
          <div class="stat">
            <span class="stat-number">100%</span>
            <span class="stat-label">Practical</span>
          </div>
        </div>
      </section>

      <!-- Quick Navigation -->
      <section class="quick-nav">
        <h2>🎯 Quick Navigation</h2>
        <div class="nav-grid">
          <!-- Forms Section -->
          <div class="nav-category">
            <h3>📝 Forms & Validation</h3>
            <button (click)="navigateTo('/login')" class="nav-btn primary">
              Login (Reactive Forms)
            </button>
            <button (click)="navigateTo('/register')" class="nav-btn">
              Register (Template-driven)
            </button>
            <button (click)="navigateTo('/dynamic-form')" class="nav-btn">
              Dynamic Forms
            </button>
          </div>

          <!-- Data & API Section -->
          <div class="nav-category">
            <h3>🌐 Data & APIs</h3>
            <button (click)="navigateTo('/users')" class="nav-btn primary">
              Users List (HttpClient)
            </button>
            <button (click)="navigateTo('/filter')" class="nav-btn">
              Real-time Filter
            </button>
            <button (click)="navigateTo('/upload')" class="nav-btn">
              File Upload
            </button>
          </div>

          <!-- RxJS & State Section -->
          <div class="nav-category">
            <h3>🔄 RxJS & State</h3>
            <button (click)="navigateTo('/rxjs')" class="nav-btn primary">
              RxJS Playground
            </button>
            <button (click)="navigateTo('/state')" class="nav-btn">
              BehaviorSubject
            </button>
            <button (click)="navigateTo('/ngrx')" class="nav-btn">
              NgRx Store
            </button>
          </div>

          <!-- Advanced Features -->
          <div class="nav-category">
            <h3>⚡ Advanced Features</h3>
            <button (click)="navigateTo('/protected')" class="nav-btn primary">
              Route Guards
            </button>
            <button (click)="navigateTo('/directive')" class="nav-btn">
              Custom Directives
            </button>
            <button (click)="navigateTo('/worker')" class="nav-btn">
              Web Workers
            </button>
          </div>
        </div>
      </section>

      <!-- Learning Path -->
      <section class="learning-path">
        <h2>📚 Recommended Learning Path</h2>
        <div class="path-steps">
          <div class="step" [class.completed]="isStepCompleted(1)">
            <span class="step-number">1</span>
            <div class="step-content">
              <h4>Basics</h4>
              <p>Components, Templates, Data Binding</p>
              <button (click)="navigateTo('/login')" class="step-btn">Start Here</button>
            </div>
          </div>
          
          <div class="step" [class.completed]="isStepCompleted(2)">
            <span class="step-number">2</span>
            <div class="step-content">
              <h4>Forms</h4>
              <p>Reactive & Template-driven Forms</p>
              <button (click)="navigateTo('/register')" class="step-btn">Continue</button>
            </div>
          </div>
          
          <div class="step" [class.completed]="isStepCompleted(3)">
            <span class="step-number">3</span>
            <div class="step-content">
              <h4>HTTP & APIs</h4>
              <p>HttpClient, Observables, Error Handling</p>
              <button (click)="navigateTo('/users')" class="step-btn">Learn More</button>
            </div>
          </div>
          
          <div class="step" [class.completed]="isStepCompleted(4)">
            <span class="step-number">4</span>
            <div class="step-content">
              <h4>Advanced</h4>
              <p>RxJS, State Management, Performance</p>
              <button (click)="navigateTo('/rxjs')" class="step-btn">Master It</button>
            </div>
          </div>
        </div>
      </section>

      <!-- Interview Tips -->
      <section class="interview-tips">
        <h2>💡 Interview Success Tips</h2>
        <div class="tips-grid">
          <div class="tip-card">
            <h4>🎯 Know the Fundamentals</h4>
            <p>Understand components, services, dependency injection, and the Angular lifecycle.</p>
          </div>
          <div class="tip-card">
            <h4>🔄 Master RxJS</h4>
            <p>Be comfortable with Observables, operators like map, filter, switchMap, and error handling.</p>
          </div>
          <div class="tip-card">
            <h4>📝 Forms Expertise</h4>
            <p>Know both reactive and template-driven forms, validation strategies, and custom validators.</p>
          </div>
          <div class="tip-card">
            <h4>⚡ Performance Optimization</h4>
            <p>Understand OnPush change detection, lazy loading, and trackBy functions.</p>
          </div>
        </div>
      </section>
    </div>
  `,
  styleUrls: ['./home.component.scss']
})
export class HomeComponent {
  // Interview Q: Why don't we need ngOnInit here?
  // A: This component doesn't need initialization logic, subscriptions, or API calls
  
  constructor(private router: Router) {
    // Interview Q: What should go in constructor vs ngOnInit?
    // A: Constructor: dependency injection, basic property initialization
    //    ngOnInit: component initialization, subscriptions, API calls
  }

  /**
   * Interview Q: How do you navigate programmatically in Angular?
   * A: Use Router.navigate() method with route array
   */
  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  /**
   * Interview Q: How would you track user progress in a real app?
   * A: Use a service with localStorage/sessionStorage or backend API
   * This is a simplified demo version
   */
  isStepCompleted(step: number): boolean {
    // Simplified logic - in real app, check user progress from service
    const visitedPages = JSON.parse(localStorage.getItem('visitedPages') || '[]');
    const requiredPages = {
      1: ['login'],
      2: ['register'],
      3: ['users'],
      4: ['rxjs']
    };
    
    return requiredPages[step as keyof typeof requiredPages]?.some(page => 
      visitedPages.includes(page)
    ) || false;
  }
}

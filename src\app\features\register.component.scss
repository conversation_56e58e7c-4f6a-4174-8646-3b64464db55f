/* REGISTER COMPONENT STYLES - Template-driven Forms Demo */

.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  padding: 2rem;
}

.register-card {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
  
  @media (max-width: 768px) {
    padding: 2rem;
    margin: 1rem;
    max-height: 95vh;
  }
}

.register-header {
  text-align: center;
  margin-bottom: 2rem;
  
  h1 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 2rem;
  }
  
  p {
    color: #666;
    margin: 0;
  }
}

.register-form {
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 0;
    }
  }
  
  .form-group {
    margin-bottom: 1.5rem;
    
    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #2c3e50;
      font-weight: 600;
      font-size: 0.9rem;
    }
    
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    input[type="password"],
    input[type="date"],
    select {
      width: 100%;
      padding: 0.8rem;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      font-size: 0.95rem;
      transition: all 0.3s ease;
      box-sizing: border-box;
      
      &:focus {
        outline: none;
        border-color: #764ba2;
        box-shadow: 0 0 0 3px rgba(118, 75, 162, 0.1);
      }
      
      &.error {
        border-color: #e74c3c;
        background-color: #fdf2f2;
      }
      
      &.success {
        border-color: #27ae60;
        background-color: #f2fdf5;
      }
      
      &::placeholder {
        color: #999;
      }
    }
    
    select {
      cursor: pointer;
      
      option {
        padding: 0.5rem;
      }
    }
    
    &.checkbox-group {
      .checkbox-label {
        display: flex;
        align-items: flex-start;
        cursor: pointer;
        font-weight: normal;
        line-height: 1.5;
        
        input[type="checkbox"] {
          margin-right: 0.75rem;
          margin-top: 0.25rem;
          width: auto;
          flex-shrink: 0;
        }
        
        .checkmark {
          flex: 1;
        }
        
        a {
          color: #764ba2;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}

.error-messages {
  margin-top: 0.5rem;
  
  small {
    display: block;
    color: #e74c3c;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.submit-btn {
  width: 100%;
  padding: 1rem;
  background: #764ba2;
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
  
  &:hover:not(:disabled) {
    background: #6a4190;
    transform: translateY(-2px);
  }
  
  &:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
  }
  
  &.loading {
    background: #95a5a6;
  }
}

.form-footer {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  
  button {
    flex: 1;
    padding: 0.6rem 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    background: white;
    color: #666;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #764ba2;
      color: #764ba2;
    }
    
    &.demo-btn:hover {
      border-color: #27ae60;
      color: #27ae60;
    }
    
    &.debug-btn:hover {
      border-color: #f39c12;
      color: #f39c12;
    }
    
    &.reset-btn:hover {
      border-color: #e74c3c;
      color: #e74c3c;
    }
  }
}

.navigation {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
  
  .nav-btn {
    padding: 0.8rem 1.5rem;
    border: 2px solid #764ba2;
    border-radius: 8px;
    background: white;
    color: #764ba2;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    text-decoration: none;
    
    &:hover {
      background: #764ba2;
      color: white;
    }
  }
}

/* Debug Section */
.debug-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e1e5e9;
  
  h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
    font-size: 1rem;
  }
  
  .debug-grid {
    display: grid;
    gap: 1rem;
    
    .debug-item {
      font-size: 0.85rem;
      
      strong {
        color: #2c3e50;
      }
      
      pre {
        background: white;
        padding: 0.5rem;
        border-radius: 4px;
        margin: 0.25rem 0 0 0;
        font-size: 0.8rem;
        overflow-x: auto;
        max-height: 200px;
        overflow-y: auto;
      }
      
      .text-success {
        color: #27ae60;
        font-weight: bold;
      }
      
      .text-error {
        color: #e74c3c;
        font-weight: bold;
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .register-container {
    padding: 1rem;
  }
  
  .register-card {
    padding: 1.5rem;
  }
  
  .form-footer {
    flex-direction: column;
    
    button {
      flex: none;
    }
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
}

/* Custom Scrollbar for Debug Section */
.debug-section pre::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.debug-section pre::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.debug-section pre::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.debug-section pre::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

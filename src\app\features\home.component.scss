/* HOME COMPONENT STYLES - Interview Question: How does CSS encapsulation work in Angular? */

.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Hero Section */
.hero {
  text-align: center;
  padding: 4rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  color: white;
  margin-bottom: 3rem;
  
  .hero-title {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 700;
    
    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
  }
  
  .hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    
    @media (max-width: 768px) {
      gap: 1.5rem;
    }
    
    .stat {
      text-align: center;
      
      .stat-number {
        display: block;
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
      }
      
      .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
      }
    }
  }
}

/* Quick Navigation */
.quick-nav {
  margin-bottom: 3rem;
  
  h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
    font-size: 2rem;
  }
  
  .nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    
    .nav-category {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      border: 1px solid #e1e5e9;
      
      h3 {
        margin-bottom: 1.5rem;
        color: #2c3e50;
        font-size: 1.3rem;
      }
      
      .nav-btn {
        display: block;
        width: 100%;
        padding: 0.8rem 1rem;
        margin-bottom: 0.8rem;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        background: white;
        color: #555;
        font-size: 0.95rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: left;
        
        &:hover {
          border-color: #667eea;
          color: #667eea;
          transform: translateY(-2px);
        }
        
        &.primary {
          background: #667eea;
          color: white;
          border-color: #667eea;
          
          &:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
          }
        }
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

/* Learning Path */
.learning-path {
  margin-bottom: 3rem;
  
  h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
    font-size: 2rem;
  }
  
  .path-steps {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 2rem;
    }
    
    // Connection line between steps
    &::before {
      content: '';
      position: absolute;
      top: 30px;
      left: 10%;
      right: 10%;
      height: 2px;
      background: #e1e5e9;
      z-index: 1;
      
      @media (max-width: 768px) {
        display: none;
      }
    }
    
    .step {
      flex: 1;
      text-align: center;
      position: relative;
      z-index: 2;
      
      .step-number {
        display: inline-block;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: #e1e5e9;
        color: #666;
        line-height: 60px;
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
      }
      
      .step-content {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
        h4 {
          margin-bottom: 0.5rem;
          color: #2c3e50;
        }
        
        p {
          color: #666;
          margin-bottom: 1rem;
          font-size: 0.9rem;
        }
        
        .step-btn {
          padding: 0.6rem 1.2rem;
          border: 2px solid #667eea;
          border-radius: 6px;
          background: white;
          color: #667eea;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            background: #667eea;
            color: white;
          }
        }
      }
      
      &.completed {
        .step-number {
          background: #27ae60;
          color: white;
        }
        
        .step-content {
          border: 2px solid #27ae60;
        }
      }
    }
  }
}

/* Interview Tips */
.interview-tips {
  h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
    font-size: 2rem;
  }
  
  .tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    
    .tip-card {
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 10px;
      border-left: 4px solid #667eea;
      
      h4 {
        margin-bottom: 1rem;
        color: #2c3e50;
        font-size: 1.1rem;
      }
      
      p {
        color: #666;
        line-height: 1.6;
        margin: 0;
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .home-container {
    padding: 1rem;
  }
  
  .nav-grid {
    grid-template-columns: 1fr;
  }
  
  .tips-grid {
    grid-template-columns: 1fr;
  }
}

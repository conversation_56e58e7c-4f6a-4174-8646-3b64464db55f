import { Component, OnInit, On<PERSON><PERSON><PERSON> } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { AuthService, User } from '../core/services/auth.service';

/**
 * PROTECTED COMPONENT - Page 4 of 20
 * 
 * 🎯 Core Concepts Demonstrated:
 * - Route Guards (canActivate) protection
 * - Authentication state management
 * - JWT token handling and display
 * - User role-based content display
 * - Subscription management with takeUntil
 * 
 * 🎤 Interview Questions This Component Answers:
 * 
 * Q1: How do route guards protect components?
 * A: Guards run before component activation. If guard returns false or redirects,
 *    component never loads. This component only loads if AuthGuard allows it.
 * 
 * Q2: How do you manage subscriptions to prevent memory leaks?
 * A: Use takeUntil pattern with Subject that completes in ngOnDestroy to
 *    automatically unsubscribe from all observables.
 * 
 * Q3: How do you display different content based on user roles?
 * A: Check user roles from AuthService and use *ngIf or other conditional
 *    rendering to show/hide content based on permissions.
 * 
 * Q4: How do you handle authentication state changes in components?
 * A: Subscribe to AuthService observables (isAuthenticated$, currentUser$)
 *    to reactively update UI when authentication state changes.
 */

@Component({
  selector: 'app-protected',
  template: `
    <div class="protected-container">
      <div class="protected-card">
        <!-- Header -->
        <div class="protected-header">
          <h1>🔒 Protected Area</h1>
          <p>You can only see this page if you're authenticated!</p>
          
          <!-- User Welcome -->
          <div class="user-welcome" *ngIf="currentUser">
            <div class="avatar">{{ getInitials(currentUser.name) }}</div>
            <div class="user-info">
              <h3>Welcome, {{ currentUser.name }}!</h3>
              <p>{{ currentUser.email }}</p>
              <div class="user-roles">
                <span class="role-badge" 
                      *ngFor="let role of currentUser.roles"
                      [class.admin]="role === 'admin'">
                  {{ role | titlecase }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Authentication Info -->
        <div class="auth-info-section">
          <h3>🔍 Authentication Information</h3>
          
          <div class="info-grid">
            <div class="info-card">
              <h4>Authentication Status</h4>
              <div class="status-indicator" [class.authenticated]="isAuthenticated">
                <span class="status-dot"></span>
                {{ isAuthenticated ? 'Authenticated' : 'Not Authenticated' }}
              </div>
            </div>

            <div class="info-card">
              <h4>Session Type</h4>
              <p>{{ getSessionType() }}</p>
            </div>

            <div class="info-card">
              <h4>Token Expiration</h4>
              <p>{{ getTokenExpiration() }}</p>
            </div>

            <div class="info-card">
              <h4>User Permissions</h4>
              <div class="permissions">
                <span class="permission" *ngIf="hasRole('user')">✅ User Access</span>
                <span class="permission" *ngIf="hasRole('admin')">👑 Admin Access</span>
                <span class="permission" *ngIf="!hasRole('admin')">❌ Admin Access</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Role-based Content -->
        <div class="role-content-section">
          <h3>📋 Role-based Content</h3>
          
          <!-- Content for all authenticated users -->
          <div class="content-card user-content">
            <h4>👤 User Content</h4>
            <p>This content is visible to all authenticated users.</p>
            <ul>
              <li>View your profile</li>
              <li>Update personal information</li>
              <li>Access basic features</li>
            </ul>
          </div>

          <!-- Admin-only content -->
          <div class="content-card admin-content" *ngIf="hasRole('admin')">
            <h4>👑 Admin Content</h4>
            <p>This content is only visible to administrators.</p>
            <ul>
              <li>Manage users</li>
              <li>System configuration</li>
              <li>View analytics</li>
              <li>Access admin panel</li>
            </ul>
            <button class="admin-btn">Access Admin Panel</button>
          </div>

          <!-- Message for non-admin users -->
          <div class="content-card restricted-content" *ngIf="!hasRole('admin')">
            <h4>🚫 Restricted Content</h4>
            <p>You need administrator privileges to access advanced features.</p>
            <p class="upgrade-message">Contact your system administrator for access.</p>
          </div>
        </div>

        <!-- Token Information (Debug) -->
        <div class="token-section" *ngIf="showTokenInfo">
          <h3>🔐 Token Information (Debug)</h3>
          <div class="token-display">
            <h4>JWT Token:</h4>
            <div class="token-parts">
              <div class="token-part header">
                <h5>Header</h5>
                <pre>{{ getTokenHeader() | json }}</pre>
              </div>
              <div class="token-part payload">
                <h5>Payload</h5>
                <pre>{{ getTokenPayload() | json }}</pre>
              </div>
              <div class="token-part signature">
                <h5>Signature</h5>
                <code>{{ getTokenSignature() }}</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="actions-section">
          <button (click)="refreshUserInfo()" class="action-btn primary">
            🔄 Refresh User Info
          </button>
          <button (click)="toggleTokenInfo()" class="action-btn">
            {{ showTokenInfo ? '🙈 Hide' : '👁️ Show' }} Token Info
          </button>
          <button (click)="testProtectedAction()" class="action-btn">
            🧪 Test Protected Action
          </button>
          <button (click)="logout()" class="action-btn danger">
            🚪 Logout
          </button>
        </div>

        <!-- Navigation -->
        <div class="navigation">
          <button (click)="goHome()" class="nav-btn">← Back to Home</button>
          <button (click)="goToUsers()" class="nav-btn">View Users List →</button>
        </div>

        <!-- Interview Tips -->
        <div class="interview-tips">
          <h3>💡 Interview Tips - Route Guards</h3>
          <div class="tips-grid">
            <div class="tip">
              <strong>Guard Types:</strong> canActivate, canActivateChild, canDeactivate, canLoad, resolve
            </div>
            <div class="tip">
              <strong>Return Values:</strong> boolean, UrlTree, Observable, Promise
            </div>
            <div class="tip">
              <strong>Use Cases:</strong> Authentication, authorization, unsaved changes, data preloading
            </div>
            <div class="tip">
              <strong>Best Practice:</strong> Keep guards simple, delegate complex logic to services
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./protected.component.scss']
})
export class ProtectedComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  currentUser: User | null = null;
  isAuthenticated = false;
  showTokenInfo = false;

  constructor(
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    // Interview Q: How do you manage subscriptions in components?
    // A: Use takeUntil pattern to automatically unsubscribe in ngOnDestroy
    
    // Subscribe to authentication state
    this.authService.isAuthenticated$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isAuth => {
        this.isAuthenticated = isAuth;
        if (!isAuth) {
          // If authentication state changes to false, redirect to login
          this.router.navigate(['/login']);
        }
      });

    // Subscribe to current user
    this.authService.currentUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe(user => {
        this.currentUser = user;
      });

    // Check for return URL from query params
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        if (params['returnUrl']) {
          console.log('User was redirected from:', params['returnUrl']);
        }
      });
  }

  ngOnDestroy(): void {
    // Interview Q: Why complete subjects in ngOnDestroy?
    // A: Prevents memory leaks by unsubscribing from all observables
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Interview Q: How do you check user roles in components?
  hasRole(role: string): boolean {
    return this.authService.hasRole(role);
  }

  getInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  }

  getSessionType(): string {
    const token = this.authService.getToken();
    if (!token) return 'No active session';
    
    const hasLocalStorage = localStorage.getItem('authToken');
    const hasSessionStorage = sessionStorage.getItem('authToken');
    
    if (hasLocalStorage) return 'Persistent (Remember Me)';
    if (hasSessionStorage) return 'Session (Browser Tab)';
    return 'Unknown';
  }

  getTokenExpiration(): string {
    if (!this.currentUser?.exp) return 'Unknown';
    
    const expDate = new Date(this.currentUser.exp * 1000);
    const now = new Date();
    const diff = expDate.getTime() - now.getTime();
    
    if (diff <= 0) return 'Expired';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m remaining`;
  }

  getTokenHeader(): any {
    const token = this.authService.getToken();
    if (!token) return null;
    
    try {
      return JSON.parse(atob(token.split('.')[0]));
    } catch {
      return null;
    }
  }

  getTokenPayload(): any {
    const token = this.authService.getToken();
    if (!token) return null;
    
    try {
      return JSON.parse(atob(token.split('.')[1]));
    } catch {
      return null;
    }
  }

  getTokenSignature(): string {
    const token = this.authService.getToken();
    return token ? token.split('.')[2] : '';
  }

  refreshUserInfo(): void {
    // In real app, this might refresh token or fetch updated user info
    console.log('Refreshing user information...');
    const user = this.authService.getCurrentUser();
    if (user) {
      this.currentUser = { ...user };
    }
  }

  toggleTokenInfo(): void {
    this.showTokenInfo = !this.showTokenInfo;
  }

  testProtectedAction(): void {
    // Simulate a protected API call
    console.log('Executing protected action...');
    alert('Protected action executed successfully! 🎉');
  }

  logout(): void {
    if (confirm('Are you sure you want to logout?')) {
      this.authService.logout();
    }
  }

  goHome(): void {
    this.router.navigate(['/']);
  }

  goToUsers(): void {
    this.router.navigate(['/users']);
  }
}

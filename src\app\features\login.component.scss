/* LOGIN COMPONENT STYLES - Reactive Forms Demo */

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.login-card {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
  
  @media (max-width: 768px) {
    padding: 2rem;
    margin: 1rem;
  }
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
  
  h1 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 2rem;
  }
  
  p {
    color: #666;
    margin: 0;
  }
}

.login-form {
  .form-group {
    margin-bottom: 1.5rem;
    
    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #2c3e50;
      font-weight: 600;
      font-size: 0.9rem;
    }
    
    input[type="email"],
    input[type="password"],
    input[type="text"] {
      width: 100%;
      padding: 1rem;
      border: 2px solid #e1e5e9;
      border-radius: 10px;
      font-size: 1rem;
      transition: all 0.3s ease;
      box-sizing: border-box;
      
      &:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
      
      &.error {
        border-color: #e74c3c;
        background-color: #fdf2f2;
      }
      
      &.success {
        border-color: #27ae60;
        background-color: #f2fdf5;
      }
      
      &::placeholder {
        color: #999;
      }
    }
    
    .password-input-container {
      position: relative;
      
      .password-toggle {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        cursor: pointer;
        font-size: 1.2rem;
        padding: 0.25rem;
        border-radius: 4px;
        
        &:hover {
          background-color: #f8f9fa;
        }
      }
    }
    
    &.checkbox-group {
      .checkbox-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        font-weight: normal;
        
        input[type="checkbox"] {
          margin-right: 0.5rem;
          width: auto;
        }
        
        .checkmark {
          margin-left: 0.25rem;
        }
      }
    }
  }
}

.error-messages {
  margin-top: 0.5rem;
  
  small {
    display: block;
    color: #e74c3c;
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.success-message {
  margin-top: 0.5rem;
  color: #27ae60;
  font-size: 0.85rem;
  font-weight: 500;
}

.submit-btn {
  width: 100%;
  padding: 1rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
  
  &:hover:not(:disabled) {
    background: #5a6fd8;
    transform: translateY(-2px);
  }
  
  &:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
  }
  
  &.loading {
    background: #95a5a6;
  }
}

.form-footer {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  
  button {
    flex: 1;
    padding: 0.6rem 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    background: white;
    color: #666;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #667eea;
      color: #667eea;
    }
    
    &.demo-btn:hover {
      border-color: #27ae60;
      color: #27ae60;
    }
    
    &.debug-btn:hover {
      border-color: #f39c12;
      color: #f39c12;
    }
    
    &.reset-btn:hover {
      border-color: #e74c3c;
      color: #e74c3c;
    }
  }
}

.navigation {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
  
  .nav-btn {
    padding: 0.8rem 1.5rem;
    border: 2px solid #667eea;
    border-radius: 8px;
    background: white;
    color: #667eea;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    text-decoration: none;
    
    &:hover {
      background: #667eea;
      color: white;
    }
  }
}

/* Debug Section */
.debug-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e1e5e9;
  
  h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
    font-size: 1rem;
  }
  
  .debug-grid {
    display: grid;
    gap: 1rem;
    
    .debug-item {
      font-size: 0.85rem;
      
      strong {
        color: #2c3e50;
      }
      
      pre {
        background: white;
        padding: 0.5rem;
        border-radius: 4px;
        margin: 0.25rem 0 0 0;
        font-size: 0.8rem;
        overflow-x: auto;
      }
      
      .text-success {
        color: #27ae60;
        font-weight: bold;
      }
      
      .text-error {
        color: #e74c3c;
        font-weight: bold;
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-container {
    padding: 1rem;
  }
  
  .login-card {
    padding: 1.5rem;
  }
  
  .form-footer {
    flex-direction: column;
    
    button {
      flex: none;
    }
  }
}

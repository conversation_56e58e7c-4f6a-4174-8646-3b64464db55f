import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { Router } from '@angular/router';

/**
 * LOGIN COMPONENT - Page 2 of 20
 * 
 * 🎯 Core Concepts Demonstrated:
 * - Reactive Forms (FormBuilder, FormGroup, FormControl)
 * - Built-in validators (required, email, minLength)
 * - Custom validators
 * - Form validation and error handling
 * - Form submission
 * 
 * 🎤 Interview Questions This Component Answers:
 * 
 * Q1: What's the difference between reactive and template-driven forms?
 * A: Reactive forms are model-driven, created in component class with explicit
 *    form controls. Template-driven forms are template-driven using directives.
 *    Reactive forms offer better testability, validation, and dynamic form creation.
 * 
 * Q2: How do you create custom validators in Angular?
 * A: Create a function that returns ValidatorFn. The function receives AbstractControl
 *    and returns ValidationErrors object or null.
 * 
 * Q3: How do you handle form validation errors?
 * A: Check form.get('field')?.errors and form.get('field')?.touched to display
 *    appropriate error messages.
 * 
 * Q4: What's the purpose of FormBuilder?
 * A: FormBuilder provides syntactic sugar for creating FormGroup, FormControl,
 *    and FormArray instances. It reduces boilerplate code.
 */

// Custom Validator - Interview Question: How to create reusable validators?
export function noSpacesValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (control.value && control.value.indexOf(' ') >= 0) {
      return { noSpaces: { value: control.value } };
    }
    return null;
  };
}

// Email domain validator - Interview Question: How to create parameterized validators?
export function emailDomainValidator(allowedDomain: string): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) return null;
    
    const email = control.value;
    if (email.endsWith(`@${allowedDomain}`)) {
      return null;
    }
    return { 
      emailDomain: { 
        requiredDomain: allowedDomain, 
        actualValue: email 
      } 
    };
  };
}

@Component({
  selector: 'app-login',
  template: `
    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <h1>🔐 Login Demo</h1>
          <p>Reactive Forms with Custom Validation</p>
        </div>

        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
          
          <!-- Email Field -->
          <div class="form-group">
            <label for="email">Email Address</label>
            <input 
              id="email"
              type="email" 
              formControlName="email" 
              placeholder="Enter your email"
              [class.error]="isFieldInvalid('email')"
              [class.success]="isFieldValid('email')">
            
            <!-- Error Messages - Interview Q: How to display validation errors? -->
            <div class="error-messages" *ngIf="isFieldInvalid('email')">
              <small *ngIf="loginForm.get('email')?.errors?.['required']">
                ❌ Email is required
              </small>
              <small *ngIf="loginForm.get('email')?.errors?.['email']">
                ❌ Please enter a valid email address
              </small>
              <small *ngIf="loginForm.get('email')?.errors?.['emailDomain']">
                ❌ Email must be from {{ loginForm.get('email')?.errors?.['emailDomain'].requiredDomain }} domain
              </small>
            </div>
            
            <div class="success-message" *ngIf="isFieldValid('email')">
              ✅ Email looks good!
            </div>
          </div>

          <!-- Password Field -->
          <div class="form-group">
            <label for="password">Password</label>
            <div class="password-input-container">
              <input 
                id="password"
                [type]="showPassword ? 'text' : 'password'" 
                formControlName="password" 
                placeholder="Enter your password"
                [class.error]="isFieldInvalid('password')"
                [class.success]="isFieldValid('password')">
              <button 
                type="button" 
                class="password-toggle"
                (click)="togglePasswordVisibility()">
                {{ showPassword ? '🙈' : '👁️' }}
              </button>
            </div>
            
            <!-- Password Validation Messages -->
            <div class="error-messages" *ngIf="isFieldInvalid('password')">
              <small *ngIf="loginForm.get('password')?.errors?.['required']">
                ❌ Password is required
              </small>
              <small *ngIf="loginForm.get('password')?.errors?.['minlength']">
                ❌ Password must be at least 6 characters long
              </small>
              <small *ngIf="loginForm.get('password')?.errors?.['noSpaces']">
                ❌ Password cannot contain spaces
              </small>
            </div>
            
            <div class="success-message" *ngIf="isFieldValid('password')">
              ✅ Password meets requirements!
            </div>
          </div>

          <!-- Remember Me -->
          <div class="form-group checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" formControlName="rememberMe">
              <span class="checkmark"></span>
              Remember me for 30 days
            </label>
          </div>

          <!-- Submit Button -->
          <button 
            type="submit" 
            class="submit-btn"
            [disabled]="loginForm.invalid || isSubmitting"
            [class.loading]="isSubmitting">
            <span *ngIf="!isSubmitting">Login</span>
            <span *ngIf="isSubmitting">Logging in... ⏳</span>
          </button>

          <!-- Form Status Debug (for interviews) -->
          <div class="debug-section" *ngIf="showDebugInfo">
            <h4>🔍 Form Debug Info (Interview Demo)</h4>
            <div class="debug-grid">
              <div class="debug-item">
                <strong>Form Valid:</strong> 
                <span [class]="loginForm.valid ? 'text-success' : 'text-error'">
                  {{ loginForm.valid }}
                </span>
              </div>
              <div class="debug-item">
                <strong>Form Touched:</strong> {{ loginForm.touched }}
              </div>
              <div class="debug-item">
                <strong>Form Dirty:</strong> {{ loginForm.dirty }}
              </div>
              <div class="debug-item">
                <strong>Form Value:</strong>
                <pre>{{ loginForm.value | json }}</pre>
              </div>
              <div class="debug-item">
                <strong>Form Errors:</strong>
                <pre>{{ getFormErrors() | json }}</pre>
              </div>
            </div>
          </div>
        </form>

        <!-- Additional Actions -->
        <div class="form-footer">
          <button (click)="fillDemoData()" class="demo-btn">
            Fill Demo Data
          </button>
          <button (click)="toggleDebugInfo()" class="debug-btn">
            {{ showDebugInfo ? 'Hide' : 'Show' }} Debug Info
          </button>
          <button (click)="resetForm()" class="reset-btn">
            Reset Form
          </button>
        </div>

        <!-- Navigation -->
        <div class="navigation">
          <button (click)="goHome()" class="nav-btn">← Back to Home</button>
          <button (click)="goToRegister()" class="nav-btn">Try Template-driven Forms →</button>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  showPassword = false;
  showDebugInfo = false;
  isSubmitting = false;

  constructor(
    private fb: FormBuilder,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Interview Q: Why create form in ngOnInit instead of constructor?
    // A: FormBuilder and other dependencies are fully initialized in ngOnInit
    this.createForm();
  }

  private createForm(): void {
    this.loginForm = this.fb.group({
      email: ['', [
        Validators.required,
        Validators.email,
        // emailDomainValidator('company.com') // Uncomment to test domain validation
      ]],
      password: ['', [
        Validators.required,
        Validators.minLength(6),
        noSpacesValidator()
      ]],
      rememberMe: [false]
    });
  }

  // Interview Q: How to check if a form field is invalid?
  isFieldInvalid(fieldName: string): boolean {
    const field = this.loginForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  // Interview Q: How to check if a form field is valid?
  isFieldValid(fieldName: string): boolean {
    const field = this.loginForm.get(fieldName);
    return !!(field && field.valid && (field.dirty || field.touched));
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  toggleDebugInfo(): void {
    this.showDebugInfo = !this.showDebugInfo;
  }

  // Interview Q: How to handle form submission?
  onSubmit(): void {
    if (this.loginForm.valid) {
      this.isSubmitting = true;
      const formValue = this.loginForm.value;
      
      // Simulate API call
      setTimeout(() => {
        console.log('Login successful:', formValue);
        
        // Store token (simplified for demo)
        localStorage.setItem('authToken', 'demo-jwt-token');
        localStorage.setItem('userEmail', formValue.email);
        
        // Track visited page for home component
        const visitedPages = JSON.parse(localStorage.getItem('visitedPages') || '[]');
        if (!visitedPages.includes('login')) {
          visitedPages.push('login');
          localStorage.setItem('visitedPages', JSON.stringify(visitedPages));
        }
        
        this.isSubmitting = false;
        this.router.navigate(['/protected']);
      }, 2000);
    } else {
      // Interview Q: How to handle invalid form submission?
      // A: Mark all fields as touched to show validation errors
      this.markFormGroupTouched(this.loginForm);
    }
  }

  // Utility method to mark all fields as touched
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  // Debug helper for interviews
  getFormErrors(): any {
    let errors: any = {};
    Object.keys(this.loginForm.controls).forEach(key => {
      const controlErrors = this.loginForm.get(key)?.errors;
      if (controlErrors) {
        errors[key] = controlErrors;
      }
    });
    return errors;
  }

  fillDemoData(): void {
    this.loginForm.patchValue({
      email: '<EMAIL>',
      password: 'password123',
      rememberMe: true
    });
  }

  resetForm(): void {
    this.loginForm.reset();
  }

  goHome(): void {
    this.router.navigate(['/']);
  }

  goToRegister(): void {
    this.router.navigate(['/register']);
  }
}

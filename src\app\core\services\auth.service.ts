import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router } from '@angular/router';

/**
 * AUTH SERVICE - Authentication Management
 * 
 * 🎯 Core Concepts Demonstrated:
 * - JWT token handling
 * - Local storage management
 * - Authentication state management with BehaviorSubject
 * - Token expiration checking
 * - Role-based access control
 * 
 * 🎤 Interview Questions This Service Answers:
 * 
 * Q1: How do you implement JWT authentication in Angular?
 * A: Store JWT token in localStorage/sessionStorage, decode payload for user info,
 *    check expiration, and include in HTTP requests via interceptors.
 * 
 * Q2: What's the difference between localStorage and sessionStorage?
 * A: localStorage persists until explicitly cleared, sessionStorage clears when
 *    browser tab closes. Use localStorage for "remember me", sessionStorage for
 *    temporary sessions.
 * 
 * Q3: How do you manage authentication state across components?
 * A: Use BehaviorSubject to emit authentication state changes that components
 *    can subscribe to for reactive updates.
 * 
 * Q4: How do you decode JWT tokens in JavaScript?
 * A: Split token by '.', base64 decode the payload (middle part), and parse JSON.
 *    Note: This only decodes, doesn't verify signature.
 */

export interface User {
  id: string;
  email: string;
  name: string;
  roles: string[];
  exp?: number; // Token expiration
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly TOKEN_KEY = 'authToken';
  private readonly USER_KEY = 'currentUser';
  
  // Interview Q: Why use BehaviorSubject for authentication state?
  // A: BehaviorSubject has initial value and emits current value to new subscribers
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasValidToken());
  private currentUserSubject = new BehaviorSubject<User | null>(this.getCurrentUserFromStorage());

  // Public observables for components to subscribe to
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(private router: Router) {
    // Check token validity on service initialization
    this.checkTokenValidity();
  }

  /**
   * Interview Q: How do you implement login functionality?
   * A: Validate credentials, store token, update authentication state
   */
  login(credentials: LoginCredentials): Observable<boolean> {
    return new Observable(observer => {
      // Simulate API call delay
      setTimeout(() => {
        // In real app, this would be an HTTP call to your auth endpoint
        if (this.validateCredentials(credentials)) {
          const mockToken = this.generateMockJWT(credentials.email);
          const user = this.decodeToken(mockToken);
          
          // Store token and user info
          const storage = credentials.rememberMe ? localStorage : sessionStorage;
          storage.setItem(this.TOKEN_KEY, mockToken);
          localStorage.setItem(this.USER_KEY, JSON.stringify(user));
          
          // Update authentication state
          this.isAuthenticatedSubject.next(true);
          this.currentUserSubject.next(user);
          
          observer.next(true);
        } else {
          observer.next(false);
        }
        observer.complete();
      }, 1000);
    });
  }

  /**
   * Interview Q: How do you implement logout functionality?
   * A: Clear tokens, reset state, redirect to login
   */
  logout(): void {
    // Clear all authentication data
    localStorage.removeItem(this.TOKEN_KEY);
    sessionStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    
    // Update authentication state
    this.isAuthenticatedSubject.next(false);
    this.currentUserSubject.next(null);
    
    // Redirect to login
    this.router.navigate(['/login']);
  }

  /**
   * Interview Q: How do you check if user is authenticated?
   * A: Check token existence and validity (not expired)
   */
  isAuthenticated(): boolean {
    return this.hasValidToken();
  }

  /**
   * Interview Q: How do you get current user information?
   * A: Decode JWT token payload or retrieve from storage
   */
  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  /**
   * Interview Q: How do you implement role-based access control?
   * A: Check user roles from token payload or user object
   */
  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user ? user.roles.includes(role) : false;
  }

  /**
   * Interview Q: How do you check if user has any of multiple roles?
   */
  hasAnyRole(roles: string[]): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;
    return roles.some(role => user.roles.includes(role));
  }

  /**
   * Interview Q: How do you get the authentication token?
   * A: Check both localStorage and sessionStorage
   */
  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY) || 
           sessionStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * Interview Q: How do you decode JWT tokens?
   * A: Split by '.', base64 decode payload, parse JSON
   */
  private decodeToken(token: string): User | null {
    try {
      const payload = token.split('.')[1];
      const decoded = JSON.parse(atob(payload));
      return {
        id: decoded.sub,
        email: decoded.email,
        name: decoded.name,
        roles: decoded.roles || ['user'],
        exp: decoded.exp
      };
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  /**
   * Interview Q: How do you check token expiration?
   * A: Compare token exp claim with current timestamp
   */
  private isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const exp = payload.exp * 1000; // Convert to milliseconds
      return Date.now() >= exp;
    } catch {
      return true; // Consider invalid tokens as expired
    }
  }

  private hasValidToken(): boolean {
    const token = this.getToken();
    return token ? !this.isTokenExpired(token) : false;
  }

  private getCurrentUserFromStorage(): User | null {
    try {
      const userJson = localStorage.getItem(this.USER_KEY);
      return userJson ? JSON.parse(userJson) : null;
    } catch {
      return null;
    }
  }

  private checkTokenValidity(): void {
    if (!this.hasValidToken()) {
      this.logout();
    }
  }

  /**
   * Mock credential validation - In real app, this would be server-side
   */
  private validateCredentials(credentials: LoginCredentials): boolean {
    // Demo credentials for testing
    const validCredentials = [
      { email: '<EMAIL>', password: 'password123' },
      { email: '<EMAIL>', password: 'admin123' },
      { email: '<EMAIL>', password: 'user123' }
    ];
    
    return validCredentials.some(cred => 
      cred.email === credentials.email && cred.password === credentials.password
    );
  }

  /**
   * Generate mock JWT token for demo purposes
   * In real app, this would come from your authentication server
   */
  private generateMockJWT(email: string): string {
    const header = { alg: 'HS256', typ: 'JWT' };
    const payload = {
      sub: '12345',
      email: email,
      name: email.split('@')[0],
      roles: email.includes('admin') ? ['admin', 'user'] : ['user'],
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24) // 24 hours
    };
    
    // This is a mock implementation - real JWT would be signed
    const encodedHeader = btoa(JSON.stringify(header));
    const encodedPayload = btoa(JSON.stringify(payload));
    const signature = 'mock-signature';
    
    return `${encodedHeader}.${encodedPayload}.${signature}`;
  }
}

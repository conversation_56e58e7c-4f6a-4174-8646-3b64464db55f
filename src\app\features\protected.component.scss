/* PROTECTED COMPONENT STYLES - Route Guards Demo */

.protected-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
  padding: 2rem;
}

.protected-card {
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  
  @media (max-width: 768px) {
    padding: 2rem;
    margin: 1rem;
  }
}

.protected-header {
  text-align: center;
  margin-bottom: 3rem;
  
  h1 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
  }
  
  p {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }
  
  .user-welcome {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    margin-top: 2rem;
    
    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
    }
    
    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: #3498db;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      font-weight: bold;
    }
    
    .user-info {
      h3 {
        margin: 0 0 0.5rem 0;
        color: #2c3e50;
      }
      
      p {
        margin: 0 0 1rem 0;
        color: #666;
      }
      
      .user-roles {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        
        @media (max-width: 768px) {
          justify-content: center;
        }
        
        .role-badge {
          padding: 0.25rem 0.75rem;
          background: #e3f2fd;
          color: #1976d2;
          border-radius: 20px;
          font-size: 0.85rem;
          font-weight: 500;
          
          &.admin {
            background: #fff3e0;
            color: #f57c00;
          }
        }
      }
    }
  }
}

.auth-info-section,
.role-content-section,
.token-section {
  margin-bottom: 3rem;
  
  h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  
  .info-card {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #3498db;
    
    h4 {
      margin: 0 0 1rem 0;
      color: #2c3e50;
      font-size: 1.1rem;
    }
    
    p {
      margin: 0;
      color: #666;
    }
    
    .status-indicator {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      
      .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #e74c3c;
        
        .authenticated & {
          background: #27ae60;
        }
      }
      
      &.authenticated {
        color: #27ae60;
        font-weight: 600;
      }
    }
    
    .permissions {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      
      .permission {
        font-size: 0.9rem;
      }
    }
  }
}

.content-card {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  padding: 2rem;
  margin-bottom: 1.5rem;
  
  h4 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
  }
  
  p {
    color: #666;
    margin-bottom: 1rem;
  }
  
  ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
    
    li {
      margin-bottom: 0.5rem;
      color: #666;
    }
  }
  
  &.user-content {
    border-color: #3498db;
    background: #f8fbff;
  }
  
  &.admin-content {
    border-color: #f39c12;
    background: #fffbf5;
    
    .admin-btn {
      padding: 0.75rem 1.5rem;
      background: #f39c12;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      
      &:hover {
        background: #e67e22;
        transform: translateY(-2px);
      }
    }
  }
  
  &.restricted-content {
    border-color: #e74c3c;
    background: #fdf2f2;
    
    .upgrade-message {
      font-style: italic;
      color: #c0392b;
    }
  }
}

.token-section {
  .token-display {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    
    h4 {
      margin: 0 0 1rem 0;
      color: #2c3e50;
    }
    
    .token-parts {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      
      .token-part {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e1e5e9;
        
        h5 {
          margin: 0 0 0.5rem 0;
          color: #2c3e50;
          font-size: 0.9rem;
        }
        
        pre {
          background: #f1f3f4;
          padding: 0.75rem;
          border-radius: 4px;
          font-size: 0.8rem;
          overflow-x: auto;
          margin: 0;
        }
        
        code {
          background: #f1f3f4;
          padding: 0.5rem;
          border-radius: 4px;
          font-size: 0.8rem;
          word-break: break-all;
        }
        
        &.header {
          border-left: 4px solid #3498db;
        }
        
        &.payload {
          border-left: 4px solid #27ae60;
        }
        
        &.signature {
          border-left: 4px solid #e74c3c;
        }
      }
    }
  }
}

.actions-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  
  .action-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid #3498db;
    border-radius: 8px;
    background: white;
    color: #3498db;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    
    &:hover {
      background: #3498db;
      color: white;
      transform: translateY(-2px);
    }
    
    &.primary {
      background: #3498db;
      color: white;
      
      &:hover {
        background: #2980b9;
      }
    }
    
    &.danger {
      border-color: #e74c3c;
      color: #e74c3c;
      
      &:hover {
        background: #e74c3c;
        color: white;
      }
    }
  }
}

.navigation {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 2rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
  
  .nav-btn {
    padding: 0.8rem 1.5rem;
    border: 2px solid #2c3e50;
    border-radius: 8px;
    background: white;
    color: #2c3e50;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    
    &:hover {
      background: #2c3e50;
      color: white;
    }
  }
}

.interview-tips {
  background: #e8f5e8;
  padding: 2rem;
  border-radius: 10px;
  border-left: 4px solid #27ae60;
  
  h3 {
    margin: 0 0 1.5rem 0;
    color: #27ae60;
  }
  
  .tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    
    .tip {
      background: white;
      padding: 1rem;
      border-radius: 8px;
      font-size: 0.9rem;
      
      strong {
        color: #27ae60;
        display: block;
        margin-bottom: 0.5rem;
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .protected-container {
    padding: 1rem;
  }
  
  .protected-card {
    padding: 1.5rem;
  }
  
  .actions-section {
    flex-direction: column;
    
    .action-btn {
      text-align: center;
    }
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .token-parts {
    grid-template-columns: 1fr;
  }
}

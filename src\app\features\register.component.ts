import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { NgForm } from '@angular/forms';

/**
 * REGISTER COMPONENT - Page 3 of 20
 * 
 * 🎯 Core Concepts Demonstrated:
 * - Template-driven forms with ngModel
 * - Two-way data binding
 * - Template reference variables
 * - Built-in validation directives
 * - Cross-field validation in templates
 * - Form submission handling
 * 
 * 🎤 Interview Questions This Component Answers:
 * 
 * Q1: What's the difference between template-driven and reactive forms?
 * A: Template-driven forms use directives in templates (ngModel, required, etc.)
 *    and are suitable for simple forms. Reactive forms are defined in component
 *    class and offer better control, testing, and complex validation.
 * 
 * Q2: How does two-way data binding work with ngModel?
 * A: [(ngModel)] is syntactic sugar for [ngModel]="property" (ngModelChange)="property=$event"
 *    It binds input value to component property and updates property when input changes.
 * 
 * Q3: What are template reference variables and how do you use them?
 * A: Template reference variables (#varName) create references to DOM elements
 *    or directives. With forms, #form="ngForm" gives access to form state and methods.
 * 
 * Q4: How do you implement cross-field validation in template-driven forms?
 * A: Use component methods called from template or create custom validators.
 *    Check field values in component and display errors conditionally.
 */

interface User {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  dateOfBirth: string;
  gender: string;
  country: string;
  agreeToTerms: boolean;
  subscribeNewsletter: boolean;
}

@Component({
  selector: 'app-register',
  template: `
    <div class="register-container">
      <div class="register-card">
        <div class="register-header">
          <h1>📝 Registration Demo</h1>
          <p>Template-driven Forms with Two-way Binding</p>
        </div>

        <!-- Interview Q: How to create template-driven form? -->
        <!-- A: Use #form="ngForm" and ngModel on form controls -->
        <form #registerForm="ngForm" (ngSubmit)="onSubmit(registerForm)" class="register-form">
          
          <!-- Name Fields -->
          <div class="form-row">
            <div class="form-group">
              <label for="firstName">First Name *</label>
              <input 
                id="firstName"
                type="text" 
                name="firstName"
                [(ngModel)]="user.firstName"
                #firstName="ngModel"
                required
                minlength="2"
                placeholder="Enter first name"
                [class.error]="firstName.invalid && firstName.touched"
                [class.success]="firstName.valid && firstName.touched">
              
              <!-- Interview Q: How to access validation state in template-driven forms? -->
              <div class="error-messages" *ngIf="firstName.invalid && firstName.touched">
                <small *ngIf="firstName.errors?.['required']">First name is required</small>
                <small *ngIf="firstName.errors?.['minlength']">First name must be at least 2 characters</small>
              </div>
            </div>

            <div class="form-group">
              <label for="lastName">Last Name *</label>
              <input 
                id="lastName"
                type="text" 
                name="lastName"
                [(ngModel)]="user.lastName"
                #lastName="ngModel"
                required
                minlength="2"
                placeholder="Enter last name"
                [class.error]="lastName.invalid && lastName.touched"
                [class.success]="lastName.valid && lastName.touched">
              
              <div class="error-messages" *ngIf="lastName.invalid && lastName.touched">
                <small *ngIf="lastName.errors?.['required']">Last name is required</small>
                <small *ngIf="lastName.errors?.['minlength']">Last name must be at least 2 characters</small>
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="form-group">
            <label for="email">Email Address *</label>
            <input 
              id="email"
              type="email" 
              name="email"
              [(ngModel)]="user.email"
              #email="ngModel"
              required
              email
              placeholder="Enter email address"
              [class.error]="email.invalid && email.touched"
              [class.success]="email.valid && email.touched">
            
            <div class="error-messages" *ngIf="email.invalid && email.touched">
              <small *ngIf="email.errors?.['required']">Email is required</small>
              <small *ngIf="email.errors?.['email']">Please enter a valid email address</small>
            </div>
          </div>

          <div class="form-group">
            <label for="phone">Phone Number</label>
            <input 
              id="phone"
              type="tel" 
              name="phone"
              [(ngModel)]="user.phone"
              #phone="ngModel"
              pattern="[0-9]{10}"
              placeholder="Enter 10-digit phone number"
              [class.error]="phone.invalid && phone.touched"
              [class.success]="phone.valid && phone.touched">
            
            <div class="error-messages" *ngIf="phone.invalid && phone.touched">
              <small *ngIf="phone.errors?.['pattern']">Phone number must be 10 digits</small>
            </div>
          </div>

          <!-- Password Fields -->
          <div class="form-row">
            <div class="form-group">
              <label for="password">Password *</label>
              <input 
                id="password"
                type="password" 
                name="password"
                [(ngModel)]="user.password"
                #password="ngModel"
                required
                minlength="6"
                placeholder="Enter password"
                [class.error]="password.invalid && password.touched"
                [class.success]="password.valid && password.touched">
              
              <div class="error-messages" *ngIf="password.invalid && password.touched">
                <small *ngIf="password.errors?.['required']">Password is required</small>
                <small *ngIf="password.errors?.['minlength']">Password must be at least 6 characters</small>
              </div>
            </div>

            <div class="form-group">
              <label for="confirmPassword">Confirm Password *</label>
              <input 
                id="confirmPassword"
                type="password" 
                name="confirmPassword"
                [(ngModel)]="user.confirmPassword"
                #confirmPassword="ngModel"
                required
                placeholder="Confirm password"
                [class.error]="(confirmPassword.invalid && confirmPassword.touched) || !passwordsMatch()"
                [class.success]="confirmPassword.valid && confirmPassword.touched && passwordsMatch()">
              
              <!-- Interview Q: How to implement cross-field validation? -->
              <div class="error-messages" *ngIf="(confirmPassword.invalid && confirmPassword.touched) || (!passwordsMatch() && confirmPassword.touched)">
                <small *ngIf="confirmPassword.errors?.['required']">Please confirm your password</small>
                <small *ngIf="confirmPassword.valid && !passwordsMatch()">Passwords do not match</small>
              </div>
            </div>
          </div>

          <!-- Personal Information -->
          <div class="form-row">
            <div class="form-group">
              <label for="dateOfBirth">Date of Birth</label>
              <input 
                id="dateOfBirth"
                type="date" 
                name="dateOfBirth"
                [(ngModel)]="user.dateOfBirth"
                #dateOfBirth="ngModel"
                [max]="maxDate"
                [class.success]="dateOfBirth.valid && dateOfBirth.touched">
            </div>

            <div class="form-group">
              <label for="gender">Gender</label>
              <select 
                id="gender"
                name="gender"
                [(ngModel)]="user.gender"
                #gender="ngModel"
                [class.success]="gender.valid && gender.touched">
                <option value="">Select gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
                <option value="prefer-not-to-say">Prefer not to say</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label for="country">Country</label>
            <select 
              id="country"
              name="country"
              [(ngModel)]="user.country"
              #country="ngModel"
              [class.success]="country.valid && country.touched">
              <option value="">Select country</option>
              <option value="us">United States</option>
              <option value="ca">Canada</option>
              <option value="uk">United Kingdom</option>
              <option value="au">Australia</option>
              <option value="de">Germany</option>
              <option value="fr">France</option>
              <option value="in">India</option>
              <option value="jp">Japan</option>
            </select>
          </div>

          <!-- Checkboxes -->
          <div class="form-group checkbox-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                name="agreeToTerms"
                [(ngModel)]="user.agreeToTerms"
                #terms="ngModel"
                required>
              <span class="checkmark"></span>
              I agree to the <a href="#" target="_blank">Terms and Conditions</a> *
            </label>
            
            <div class="error-messages" *ngIf="terms.invalid && terms.touched">
              <small *ngIf="terms.errors?.['required']">You must agree to the terms and conditions</small>
            </div>
          </div>

          <div class="form-group checkbox-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                name="subscribeNewsletter"
                [(ngModel)]="user.subscribeNewsletter">
              <span class="checkmark"></span>
              Subscribe to our newsletter for updates
            </label>
          </div>

          <!-- Submit Button -->
          <button 
            type="submit" 
            class="submit-btn"
            [disabled]="registerForm.invalid || !passwordsMatch() || isSubmitting"
            [class.loading]="isSubmitting">
            <span *ngIf="!isSubmitting">Create Account</span>
            <span *ngIf="isSubmitting">Creating Account... ⏳</span>
          </button>

          <!-- Form Debug Info -->
          <div class="debug-section" *ngIf="showDebugInfo">
            <h4>🔍 Form Debug Info (Interview Demo)</h4>
            <div class="debug-grid">
              <div class="debug-item">
                <strong>Form Valid:</strong> 
                <span [class]="registerForm.valid ? 'text-success' : 'text-error'">
                  {{ registerForm.valid }}
                </span>
              </div>
              <div class="debug-item">
                <strong>Passwords Match:</strong> 
                <span [class]="passwordsMatch() ? 'text-success' : 'text-error'">
                  {{ passwordsMatch() }}
                </span>
              </div>
              <div class="debug-item">
                <strong>Form Submitted:</strong> {{ registerForm.submitted }}
              </div>
              <div class="debug-item">
                <strong>User Data:</strong>
                <pre>{{ user | json }}</pre>
              </div>
            </div>
          </div>
        </form>

        <!-- Form Actions -->
        <div class="form-footer">
          <button (click)="fillDemoData()" class="demo-btn">Fill Demo Data</button>
          <button (click)="toggleDebugInfo()" class="debug-btn">
            {{ showDebugInfo ? 'Hide' : 'Show' }} Debug Info
          </button>
          <button (click)="resetForm(registerForm)" class="reset-btn">Reset Form</button>
        </div>

        <!-- Navigation -->
        <div class="navigation">
          <button (click)="goToLogin()" class="nav-btn">← Try Reactive Forms</button>
          <button (click)="goHome()" class="nav-btn">Back to Home →</button>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./register.component.scss']
})
export class RegisterComponent {
  showDebugInfo = false;
  isSubmitting = false;
  maxDate: string;

  // Interview Q: How does two-way data binding work?
  // A: [(ngModel)] creates two-way binding between template and component property
  user: User = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    dateOfBirth: '',
    gender: '',
    country: '',
    agreeToTerms: false,
    subscribeNewsletter: false
  };

  constructor(private router: Router) {
    // Set max date to 18 years ago
    const today = new Date();
    today.setFullYear(today.getFullYear() - 18);
    this.maxDate = today.toISOString().split('T')[0];
  }

  // Interview Q: How to implement custom validation logic in template-driven forms?
  passwordsMatch(): boolean {
    return this.user.password === this.user.confirmPassword;
  }

  // Interview Q: How to handle form submission in template-driven forms?
  onSubmit(form: NgForm): void {
    if (form.valid && this.passwordsMatch()) {
      this.isSubmitting = true;
      
      // Simulate API call
      setTimeout(() => {
        console.log('Registration successful:', this.user);
        
        // Track visited page
        const visitedPages = JSON.parse(localStorage.getItem('visitedPages') || '[]');
        if (!visitedPages.includes('register')) {
          visitedPages.push('register');
          localStorage.setItem('visitedPages', JSON.stringify(visitedPages));
        }
        
        this.isSubmitting = false;
        alert('Registration successful! Redirecting to login...');
        this.router.navigate(['/login']);
      }, 2000);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(form.controls).forEach(key => {
        form.controls[key].markAsTouched();
      });
    }
  }

  fillDemoData(): void {
    this.user = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '1234567890',
      password: 'password123',
      confirmPassword: 'password123',
      dateOfBirth: '1990-01-01',
      gender: 'male',
      country: 'us',
      agreeToTerms: true,
      subscribeNewsletter: true
    };
  }

  resetForm(form: NgForm): void {
    form.resetForm();
    this.user = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      dateOfBirth: '',
      gender: '',
      country: '',
      agreeToTerms: false,
      subscribeNewsletter: false
    };
  }

  toggleDebugInfo(): void {
    this.showDebugInfo = !this.showDebugInfo;
  }

  goToLogin(): void {
    this.router.navigate(['/login']);
  }

  goHome(): void {
    this.router.navigate(['/']);
  }
}
